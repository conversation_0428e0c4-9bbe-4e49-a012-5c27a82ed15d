package com.fishing.service;

/**
 * 收藏服务接口
 */
public interface BookmarkService {

    /**
     * 收藏动态
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     */
    void bookmarkMoment(Long userId, Long momentId);

    /**
     * 取消收藏动态
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     */
    void unbookmarkMoment(Long userId, Long momentId);

    /**
     * 检查动态是否已收藏
     *
     * @param userId   用户ID
     * @param momentId 动态ID
     * @return 是否已收藏
     */
    boolean isBookmarked(Long userId, Long momentId);
}
