package com.fishing.service;

import com.fishing.vo.notification.NotificationVO;

import java.util.List;

/**
 * 通知服务接口
 */
public interface NotificationService {

    /**
     * 获取用户通知列表
     *
     * @param userId     用户ID
     * @param page       页码
     * @param size       每页大小
     * @param unreadOnly 是否只获取未读通知
     * @return 通知列表
     */
    List<NotificationVO> getUserNotifications(Long userId, Integer page, Integer size, Boolean unreadOnly);

    /**
     * 标记通知为已读
     *
     * @param userId         用户ID
     * @param notificationId 通知ID
     */
    void markAsRead(Long userId, Long notificationId);

    /**
     * 标记所有通知为已读
     *
     * @param userId 用户ID
     */
    void markAllAsRead(Long userId);

    /**
     * 获取未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int getUnreadCount(Long userId);

    /**
     * 删除通知
     *
     * @param userId         用户ID
     * @param notificationId 通知ID
     */
    void deleteNotification(Long userId, Long notificationId);

    /**
     * 创建通知
     *
     * @param userId      接收用户ID
     * @param type        通知类型
     * @param title       标题
     * @param content     内容
     * @param relatedType 关联类型
     * @param relatedId   关联ID
     * @param imageUrl    图片URL
     */
    void createNotification(Long userId, String type, String title, String content,
                          String relatedType, Long relatedId, String imageUrl);
}
