package com.fishing.service;

import com.fishing.dto.report.ReportMomentDTO;
import com.fishing.dto.report.ReportUserDTO;

/**
 * 举报服务接口
 */
public interface ReportService {

    /**
     * 举报动态
     *
     * @param userId    举报用户ID
     * @param reportDTO 举报信息
     */
    void reportMoment(Long userId, ReportMomentDTO reportDTO);

    /**
     * 举报用户
     *
     * @param userId    举报用户ID
     * @param reportDTO 举报信息
     */
    void reportUser(Long userId, ReportUserDTO reportDTO);
}
