import 'dart:convert';
// Note: this is a standalone test, in a real Flutter app, this import would work
// For this test, we'll just verify the logic concepts

class CaughtFish {
  final String? id;
  final int? count;
  final double? weight;
  final int? fishTypeId;
  final String? fishTypeName;

  CaughtFish({
    this.id,
    this.count,
    this.weight,
    this.fishTypeId,
    this.fishTypeName,
  });

  factory CaughtFish.fromJson(Map<String, dynamic> json) {
    return CaughtFish(
      id: json['id'] as String?,
      count: json['count'] as int?,
      weight: (json['weight'] as num?)?.toDouble(),
      fishTypeId: json['fishTypeId'] as int?,
      fishTypeName: json['fishTypeName'] as String?,
    );
  }
}

class FishingCatchData {
  final List<CaughtFish>? caughtFishes;
  final double? totalWeight;
  final String? fishingMethod;
  final String? weatherConditions;
  final List<String>? catchImages;

  FishingCatchData({
    this.caughtFishes,
    this.totalWeight,
    this.fishingMethod,
    this.weatherConditions,
    this.catchImages,
  });

  factory FishingCatchData.fromJson(Map<String, dynamic> json) {
    return FishingCatchData(
      caughtFishes: (json['caughtFishes'] as List<dynamic>?)
          ?.map((e) => CaughtFish.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalWeight: (json['totalWeight'] as num?)?.toDouble(),
      fishingMethod: json['fishingMethod'] as String?,
      weatherConditions: json['weatherConditions'] as String?,
      catchImages: (json['catchImages'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }

  String? get primaryFishSpecies {
    if (caughtFishes?.isNotEmpty == true) {
      return caughtFishes!.first.fishTypeName;
    }
    return null;
  }

  int get totalCount {
    if (caughtFishes?.isNotEmpty == true) {
      return caughtFishes!.fold(0, (sum, fish) => sum + (fish.count ?? 0));
    }
    return 0;
  }
}

class MomentTypeDataParser {
  static FishingCatchData? parseFishingCatch(String? typeSpecificData) {
    if (typeSpecificData == null || typeSpecificData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(typeSpecificData);
      return FishingCatchData.fromJson(data);
    } catch (e) {
      print('Error parsing fishing catch data: $e');
      return null;
    }
  }

  static String getMomentTypeDisplayName(String? momentType) {
    switch (momentType) {
      case 'fishing_catch':
        return '钓获分享';
      case 'equipment':
        return '装备展示';
      case 'technique':
        return '技巧分享';
      case 'question':
        return '问答求助';
      default:
        return '动态分享';
    }
  }
}

void main() {
  print('=== Testing Moment Type Data Parsing ===\n');

  // Test data that matches the backend structure
  final testFishingCatchData = {
    "catchImages": [],
    "totalWeight": 0.5,
    "caughtFishes": [
      {
        "id": "1748162923002",
        "count": 1,
        "weight": 0.5,
        "fishTypeId": 3,
        "fishTypeName": "鲢鳙"
      }
    ],
    "fishingMethod": "台钓",
    "weatherConditions": "晴天"
  };

  print('1. Testing Fishing Catch Data Parsing:');
  print('Input data: ${jsonEncode(testFishingCatchData)}');

  try {
    final result = MomentTypeDataParser.parseFishingCatch(
        jsonEncode(testFishingCatchData));

    if (result != null) {
      print('✅ Parsing successful!');
      print('  - Primary fish species: ${result.primaryFishSpecies}');
      print('  - Total weight: ${result.totalWeight}kg');
      print('  - Total count: ${result.totalCount}');
      print('  - Fishing method: ${result.fishingMethod}');
      print('  - Weather conditions: ${result.weatherConditions}');
      print('  - Caught fishes count: ${result.caughtFishes?.length ?? 0}');

      if (result.caughtFishes?.isNotEmpty == true) {
        print('  - Fish details:');
        for (var fish in result.caughtFishes!) {
          print(
              '    * ${fish.fishTypeName}: ${fish.count} 条, ${fish.weight}kg');
        }
      }
    } else {
      print('❌ Parsing failed - result is null');
    }
  } catch (e) {
    print('❌ Parsing failed with error: $e');
  }

  print('\n2. Testing Moment Type Display Names:');
  final momentTypes = [
    'fishing_catch',
    'equipment',
    'technique',
    'question',
    'unknown'
  ];

  for (final type in momentTypes) {
    final displayName = MomentTypeDataParser.getMomentTypeDisplayName(type);
    print('  - $type -> $displayName');
  }

  print('\n=== Test Complete ===');
}
