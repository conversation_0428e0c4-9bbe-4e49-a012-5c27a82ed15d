import 'dart:convert';

/// 动态类型特定数据解析器
class MomentTypeDataParser {
  /// 解析钓获分享数据
  static FishingCatchData? parseFishingCatch(String? typeSpecificData) {
    if (typeSpecificData == null || typeSpecificData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(typeSpecificData);
      return FishingCatchData.fromJson(data);
    } catch (e) {
      print('Error parsing fishing catch data: $e');
      return null;
    }
  }

  /// 解析装备展示数据
  static EquipmentData? parseEquipment(String? typeSpecificData) {
    if (typeSpecificData == null || typeSpecificData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(typeSpecificData);
      return EquipmentData.fromJson(data);
    } catch (e) {
      print('Error parsing equipment data: $e');
      return null;
    }
  }

  /// 解析技巧分享数据
  static TechniqueData? parseTechnique(String? typeSpecificData) {
    if (typeSpecificData == null || typeSpecificData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(typeSpecificData);
      return TechniqueData.fromJson(data);
    } catch (e) {
      print('Error parsing technique data: $e');
      return null;
    }
  }

  /// 解析问答求助数据
  static QuestionData? parseQuestion(String? typeSpecificData) {
    if (typeSpecificData == null || typeSpecificData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(typeSpecificData);
      return QuestionData.fromJson(data);
    } catch (e) {
      print('Error parsing question data: $e');
      return null;
    }
  }

  /// 根据动态类型获取显示名称
  static String getMomentTypeDisplayName(String? momentType) {
    switch (momentType) {
      case 'fishing_catch':
        return '钓获分享';
      case 'equipment':
        return '装备展示';
      case 'technique':
        return '技巧分享';
      case 'question':
        return '问答求助';
      default:
        return '动态分享';
    }
  }
}

/// 钓获分享数据
class FishingCatchData {
  final List<CaughtFish>? caughtFishes;
  final double? totalWeight;
  final String? fishingMethod;
  final String? weatherConditions;
  final List<String>? catchImages;

  FishingCatchData({
    this.caughtFishes,
    this.totalWeight,
    this.fishingMethod,
    this.weatherConditions,
    this.catchImages,
  });

  factory FishingCatchData.fromJson(Map<String, dynamic> json) {
    return FishingCatchData(
      caughtFishes: (json['caughtFishes'] as List<dynamic>?)
          ?.map((e) => CaughtFish.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalWeight: (json['totalWeight'] as num?)?.toDouble(),
      fishingMethod: json['fishingMethod'] as String?,
      weatherConditions: json['weatherConditions'] as String?,
      catchImages: (json['catchImages'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }

  // 获取主要鱼种名称（取第一条鱼的名称）
  String? get primaryFishSpecies {
    if (caughtFishes?.isNotEmpty == true) {
      return caughtFishes!.first.fishTypeName;
    }
    return null;
  }

  // 获取总数量
  int get totalCount {
    if (caughtFishes?.isNotEmpty == true) {
      return caughtFishes!.fold(0, (sum, fish) => sum + (fish.count ?? 0));
    }
    return 0;
  }
}

/// 单条鱼类数据
class CaughtFish {
  final String? id;
  final int? count;
  final double? weight;
  final int? fishTypeId;
  final String? fishTypeName;

  CaughtFish({
    this.id,
    this.count,
    this.weight,
    this.fishTypeId,
    this.fishTypeName,
  });

  factory CaughtFish.fromJson(Map<String, dynamic> json) {
    return CaughtFish(
      id: json['id'] as String?,
      count: json['count'] as int?,
      weight: (json['weight'] as num?)?.toDouble(),
      fishTypeId: json['fishTypeId'] as int?,
      fishTypeName: json['fishTypeName'] as String?,
    );
  }
}

/// 装备展示数据
class EquipmentData {
  final String? equipmentName;
  final String? brand;
  final String? model;
  final String? price;
  final String? description;
  final String? purchaseLink;

  EquipmentData({
    this.equipmentName,
    this.brand,
    this.model,
    this.price,
    this.description,
    this.purchaseLink,
  });

  factory EquipmentData.fromJson(Map<String, dynamic> json) {
    return EquipmentData(
      equipmentName: json['equipmentName'] as String?,
      brand: json['brand'] as String?,
      model: json['model'] as String?,
      price: json['price'] as String?,
      description: json['description'] as String?,
      purchaseLink: json['purchaseLink'] as String?,
    );
  }
}

/// 技巧分享数据
class TechniqueData {
  final String? techniqueName;
  final String? category;
  final String? difficulty;
  final String? description;
  final List<String>? steps;

  TechniqueData({
    this.techniqueName,
    this.category,
    this.difficulty,
    this.description,
    this.steps,
  });

  factory TechniqueData.fromJson(Map<String, dynamic> json) {
    return TechniqueData(
      techniqueName: json['techniqueName'] as String?,
      category: json['category'] as String?,
      difficulty: json['difficulty'] as String?,
      description: json['description'] as String?,
      steps:
          (json['steps'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
    );
  }
}

/// 问答求助数据
class QuestionData {
  final String? questionTitle;
  final String? questionType;
  final String? urgency;
  final String? situation;
  final List<String>? tags;

  QuestionData({
    this.questionTitle,
    this.questionType,
    this.urgency,
    this.situation,
    this.tags,
  });

  factory QuestionData.fromJson(Map<String, dynamic> json) {
    return QuestionData(
      questionTitle: json['questionTitle'] as String?,
      questionType: json['questionType'] as String?,
      urgency: json['urgency'] as String?,
      situation: json['situation'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
    );
  }
}
