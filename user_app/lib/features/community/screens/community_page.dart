import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late CommunityViewModel _communityViewModel;
  late AnimationController _fabAnimationController;

  String _selectedFilter = "全部";
  final List<String> _filterOptions = ["全部", "钓获分享", "装备展示", "技巧分享", "问答求助"];

  String _sortBy = "latest"; // latest, hottest
  List<String> _selectedTags = [];
  bool _showOnlyFollowing = false;

  // 控制FAB显示/隐藏
  bool _showFab = true;
  double _lastScrollPosition = 0;

  @override
  void initState() {
    super.initState();
    _communityViewModel = context.read<CommunityViewModel>();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fabAnimationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initialLoad() async {
    await _loadData(refresh: true);
  }

  void _scrollListener() {
    // 加载更多
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_communityViewModel.isLoading && _communityViewModel.hasMore) {
        _loadMoreData();
      }
    }

    // FAB 显示/隐藏动画
    final currentPosition = _scrollController.position.pixels;
    if (currentPosition > _lastScrollPosition &&
        _showFab &&
        currentPosition > 100) {
      setState(() => _showFab = false);
      _fabAnimationController.reverse();
    } else if (currentPosition < _lastScrollPosition && !_showFab) {
      setState(() => _showFab = true);
      _fabAnimationController.forward();
    }
    _lastScrollPosition = currentPosition;
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (_communityViewModel.isLoading && !refresh) return;

    try {
      _applyFiltersToViewModel();
      await _communityViewModel.loadMoments(refresh: refresh);
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) setState(() {});
    }
  }

  Future<void> _loadMoreData() async {
    if (_communityViewModel.isLoading || !_communityViewModel.hasMore) return;

    try {
      await _communityViewModel.loadMoments();
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _applyFiltersToViewModel() {
    if (_selectedFilter != "全部") {
      _communityViewModel.setMomentTypeFilter(_selectedFilter);
    }
  }

  @override
  Widget build(BuildContext context) {
    final communityViewModel = context.watch<CommunityViewModel>();
    final isLoading = communityViewModel.isLoading;
    final moments = communityViewModel.moments;
    final hasMore = communityViewModel.hasMore;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: RefreshIndicator(
        onRefresh: () => _loadData(refresh: true),
        child: CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            // 简化的 App Bar
            SliverAppBar(
              floating: true,
              backgroundColor: Colors.white,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                '社区',
                style: TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
              actions: [
                // 排序按钮
                IconButton(
                  onPressed: _showSortOptions,
                  icon: Icon(
                    _sortBy == 'latest'
                        ? Icons.schedule
                        : Icons.local_fire_department,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                // 搜索按钮
                IconButton(
                  onPressed: _showSearchDialog,
                  icon: Icon(Icons.search, color: Colors.grey.shade700),
                ),
                // 通知按钮
                IconButton(
                  onPressed: _showNotifications,
                  icon: Stack(
                    children: [
                      Icon(Icons.notifications_outlined,
                          color: Colors.grey.shade700),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),

            // 优化的筛选栏
            SliverPersistentHeader(
              pinned: true,
              delegate: _OptimizedFilterBarDelegate(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      // 主筛选栏
                      Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            // 快速筛选：仅关注
                            if (isAuthenticated())
                              GestureDetector(
                                onTap: () {
                                  HapticFeedback.selectionClick();
                                  setState(() =>
                                      _showOnlyFollowing = !_showOnlyFollowing);
                                  _loadData(refresh: true);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: _showOnlyFollowing
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _showOnlyFollowing
                                            ? Icons.people
                                            : Icons.people_outline,
                                        size: 16,
                                        color: _showOnlyFollowing
                                            ? Colors.white
                                            : Colors.grey.shade700,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '关注',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: _showOnlyFollowing
                                              ? Colors.white
                                              : Colors.grey.shade700,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                            if (isAuthenticated()) const SizedBox(width: 8),

                            // 类型筛选
                            Expanded(
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _filterOptions.length,
                                itemBuilder: (context, index) {
                                  final filter = _filterOptions[index];
                                  final isSelected = _selectedFilter == filter;
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        HapticFeedback.selectionClick();
                                        setState(
                                            () => _selectedFilter = filter);
                                        _loadData(refresh: true);
                                      },
                                      child: Chip(
                                        label: Text(filter),
                                        backgroundColor: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.transparent,
                                        side: BorderSide(
                                          color: isSelected
                                              ? Theme.of(context).primaryColor
                                              : Colors.grey.shade300,
                                        ),
                                        labelStyle: TextStyle(
                                          color: isSelected
                                              ? Colors.white
                                              : Colors.grey.shade700,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),

                            // 更多筛选
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _showAdvancedFilters,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _selectedTags.isNotEmpty
                                      ? Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.1)
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Badge(
                                  isLabelVisible: _selectedTags.isNotEmpty,
                                  label: Text(_selectedTags.length.toString()),
                                  child: Icon(
                                    Icons.filter_list,
                                    size: 20,
                                    color: _selectedTags.isNotEmpty
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade700,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 标签展示栏（如果有选中的标签）
                      if (_selectedTags.isNotEmpty)
                        Container(
                          height: 36,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: _selectedTags
                                .map((tag) => Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      child: Chip(
                                        label: Text(tag),
                                        deleteIcon:
                                            const Icon(Icons.close, size: 16),
                                        onDeleted: () {
                                          setState(
                                              () => _selectedTags.remove(tag));
                                          _loadData(refresh: true);
                                        },
                                        backgroundColor: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1),
                                        deleteIconColor:
                                            Theme.of(context).primaryColor,
                                        labelStyle: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                          fontSize: 12,
                                        ),
                                        padding: EdgeInsets.zero,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // 内容区域
            if (isLoading && moments.isEmpty)
              SliverToBoxAdapter(
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              _buildOptimizedMomentsList(moments, isLoading, hasMore),
          ],
        ),
      ),

      // 优化的 FAB
      floatingActionButton: ScaleTransition(
        scale: _fabAnimationController,
        child: FloatingActionButton.extended(
          onPressed: _showPublishOptions,
          backgroundColor: Theme.of(context).primaryColor,
          icon: const Icon(Icons.edit),
          label: const Text('发布'),
        ),
      ),
    );
  }

  // 优化的动态列表
  Widget _buildOptimizedMomentsList(moments, bool isLoading, bool hasMore) {
    if (moments.isEmpty && !isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bubble_chart_outlined,
                size: 80,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                _showOnlyFollowing ? '还没有关注的人发布动态' : '暂无动态',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _showOnlyFollowing ? '去发现更多有趣的钓友吧' : '成为第一个分享的人',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              OutlinedButton.icon(
                onPressed: _showOnlyFollowing
                    ? () {
                        setState(() => _showOnlyFollowing = false);
                        _loadData(refresh: true);
                      }
                    : _showPublishOptions,
                icon: Icon(_showOnlyFollowing ? Icons.explore : Icons.add),
                label: Text(_showOnlyFollowing ? '发现动态' : '发布动态'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == moments.length) {
            if (isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              );
            } else if (!hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: Column(
                    children: [
                      Icon(Icons.check_circle_outline,
                          color: Colors.grey.shade400, size: 32),
                      const SizedBox(height: 8),
                      Text(
                        '已经到底了',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
            return const SizedBox(height: 80);
          }

          return _buildOptimizedMomentCard(moments[index]);
        },
        childCount: moments.isEmpty ? 0 : moments.length + 1,
      ),
    );
  }

  // 优化的动态卡片
  Widget _buildOptimizedMomentCard(moment) {
    final bool isLiked = moment.liked ?? false;
    final bool hasImages = moment.images != null && moment.images!.isNotEmpty;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showMomentDetail(moment),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息头部 - 简化设计
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 头像
                  GestureDetector(
                    onTap: () => _showUserProfile(moment.publisher),
                    child: Hero(
                      tag: 'avatar_${moment.id}',
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: moment.publisher.avatarUrl != null
                              ? DecorationImage(
                                  image:
                                      NetworkImage(moment.publisher.avatarUrl!),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: Colors.grey.shade200,
                        ),
                        child: moment.publisher.avatarUrl == null
                            ? Icon(Icons.person,
                                color: Colors.grey.shade400, size: 20)
                            : null,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 用户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              moment.publisher.name.isNotEmpty
                                  ? moment.publisher.name
                                  : '匿名用户',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                            ),
                            if (moment.momentType != null) ...[
                              const SizedBox(width: 8),
                              _buildMomentTypeTag(moment.momentType),
                            ],
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              _formatTimeAgo(moment.createdAt),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade500,
                              ),
                            ),
                            if (moment.fishingSpotName != null) ...[
                              const SizedBox(width: 8),
                              Text('·',
                                  style:
                                      TextStyle(color: Colors.grey.shade500)),
                              const SizedBox(width: 8),
                              Flexible(
                                child: GestureDetector(
                                  onTap: () => _navigateToFishingSpot(
                                      moment.fishingSpotId),
                                  child: Text(
                                    moment.fishingSpotName!,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 更多操作
                  IconButton(
                    onPressed: () => _showMoreOptions(moment),
                    icon: Icon(Icons.more_horiz, color: Colors.grey.shade600),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // 内容区域
            if (moment.content != null && moment.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: Text(
                  moment.content!,
                  style: const TextStyle(
                    fontSize: 15,
                    height: 1.5,
                  ),
                  maxLines: 5,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

            // 图片展示 - 优化布局
            if (hasImages) _buildOptimizedImageSection(moment.images!),

            // 标签
            if (moment.tags != null && moment.tags!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                child: Wrap(
                  spacing: 8,
                  children: moment.tags!
                      .map<Widget>((tag) => GestureDetector(
                            onTap: () => _searchByTag(tag),
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                fontSize: 13,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),

            // 交互栏 - 更简洁的设计
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 点赞
                  _buildInteractionItem(
                    icon: isLiked ? Icons.favorite : Icons.favorite_border,
                    count: moment.likeCount ?? 0,
                    color: isLiked ? Colors.red : null,
                    onTap: () => _toggleLike(moment.id),
                  ),
                  const SizedBox(width: 24),

                  // 评论
                  _buildInteractionItem(
                    icon: Icons.mode_comment_outlined,
                    count: moment.commentCount ?? 0,
                    onTap: () => _showMomentDetail(moment),
                  ),

                  const Spacer(),

                  // 分享和收藏
                  IconButton(
                    onPressed: () => _shareMoment(moment),
                    icon: Icon(Icons.share_outlined, size: 20),
                    color: Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    onPressed: () => _toggleBookmark(moment.id),
                    icon: Icon(
                      moment.bookmarked ?? false
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      size: 20,
                    ),
                    color: moment.bookmarked ?? false
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 优化的图片展示
  Widget _buildOptimizedImageSection(List images) {
    if (images.length == 1) {
      // 单图 - 大图展示
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: Image.network(
              images[0].imageUrl ?? images[0],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.shade200,
                  child: Icon(Icons.broken_image, color: Colors.grey.shade400),
                );
              },
            ),
          ),
        ),
      );
    }

    // 多图 - 网格展示
    final displayCount = images.length > 4 ? 4 : images.length;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: displayCount,
        itemBuilder: (context, index) {
          final isLast = index == displayCount - 1;
          final remainingCount = images.length - displayCount;

          return Stack(
            fit: StackFit.expand,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  images[index].imageUrl ?? images[index],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade200,
                      child:
                          Icon(Icons.broken_image, color: Colors.grey.shade400),
                    );
                  },
                ),
              ),
              if (isLast && remainingCount > 0)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '+$remainingCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  // 交互项组件
  Widget _buildInteractionItem({
    required IconData icon,
    required int count,
    Color? color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Row(
        children: [
          Icon(
            icon,
            size: 22,
            color: color ?? Colors.grey.shade700,
          ),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Text(
              count > 999 ? '999+' : count.toString(),
              style: TextStyle(
                fontSize: 14,
                color: color ?? Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 动态类型标签
  Widget _buildMomentTypeTag(String? momentType) {
    if (momentType == null) return const SizedBox.shrink();

    String displayText;
    IconData icon;
    Color color;

    switch (momentType) {
      case 'fishing_catch':
        displayText = '钓获';
        icon = Icons.catching_pokemon;
        color = Colors.green;
        break;
      case 'equipment':
        displayText = '装备';
        icon = Icons.construction;
        color = Colors.blue;
        break;
      case 'technique':
        displayText = '技巧';
        icon = Icons.lightbulb_outline;
        color = Colors.orange;
        break;
      case 'question':
        displayText = '求助';
        icon = Icons.help_outline;
        color = Colors.purple;
        break;
      default:
        displayText = '动态';
        icon = Icons.article_outlined;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 3),
          Text(
            displayText,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // 排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '排序方式',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('最新发布'),
                  trailing: _sortBy == 'latest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    setState(() => _sortBy = 'latest');
                    _loadData(refresh: true);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_fire_department),
                  title: const Text('最热门'),
                  subtitle: const Text('按互动量排序'),
                  trailing: _sortBy == 'hottest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    setState(() => _sortBy = 'hottest');
                    _loadData(refresh: true);
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  // 高级筛选
  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        List<String> localSelectedTags = List.from(_selectedTags);

        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '标签筛选',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                        TextButton(
                          onPressed: () =>
                              setState(() => localSelectedTags.clear()),
                          child: const Text('清除'),
                        ),
                      ],
                    ),
                  ),

                  // Tags
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          '新手入门',
                          '路亚技巧',
                          '台钓',
                          '野钓',
                          '黑坑',
                          '饵料配方',
                          '装备推荐',
                          '钓点分享',
                          '冬季钓鱼',
                          '鲫鱼',
                          '鲤鱼',
                          '草鱼',
                          '鲢鳙',
                          '翘嘴',
                          '鲈鱼'
                        ].map((tag) {
                          final isSelected = localSelectedTags.contains(tag);
                          return FilterChip(
                            label: Text(tag),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  localSelectedTags.add(tag);
                                } else {
                                  localSelectedTags.remove(tag);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // Apply button
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          this.setState(
                              () => _selectedTags = localSelectedTags);
                          _loadData(refresh: true);
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child:
                            const Text('应用筛选', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // 发布选项 - 优化设计
  void _showPublishOptions() {
    if (!isAuthenticated()) {
      _showLoginDialog('发布动态');
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '发布动态',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),

                // 发布选项网格
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildPublishOption(
                        icon: Icons.photo_camera,
                        label: '钓获分享',
                        color: Colors.green,
                        onTap: () {
                          Navigator.pop(context);
                          context.push(AppRoutes.publishMoment);
                        },
                      ),
                      _buildPublishOption(
                        icon: Icons.lightbulb,
                        label: '技巧分享',
                        color: Colors.orange,
                        onTap: () {
                          Navigator.pop(context);
                          // context.push(AppRoutes.publishTips);
                        },
                      ),
                      _buildPublishOption(
                        icon: Icons.help,
                        label: '提问求助',
                        color: Colors.purple,
                        onTap: () {
                          Navigator.pop(context);
                          // context.push(AppRoutes.publishQuestion);
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPublishOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // 其他辅助方法
  void _showMoreOptions(moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bookmark_outline),
                  title: const Text('收藏动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _toggleBookmark(moment.id);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share_outlined),
                  title: const Text('分享动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _shareMoment(moment);
                  },
                ),
                if (!_isOwnMoment(moment))
                  ListTile(
                    leading: const Icon(Icons.person_add_outlined),
                    title: Text(moment.followed ?? false ? '取消关注' : '关注作者'),
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFollow(moment.publisher.id);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.flag_outlined),
                  title: const Text('举报'),
                  onTap: () {
                    Navigator.pop(context);
                    _reportMoment(moment);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Actions
  void _showSearchDialog() {
    // 实现搜索对话框
  }

  void _showNotifications() {
    // 导航到通知页面
  }

  void _showMomentDetail(moment) {
    context.pushNamed(
      'moment_detail',
      pathParameters: {'id': moment.id.toString()},
      extra: moment,
    );
  }

  void _showUserProfile(user) {
    // 导航到用户主页
  }

  void _toggleLike(int momentId) {
    _communityViewModel.toggleLikeMoment(momentId);
  }

  void _toggleFollow(int userId) {
    _communityViewModel.toggleFollowUser(userId);
  }

  void _toggleBookmark(int momentId) {
    // 实现收藏功能
  }

  void _shareMoment(moment) {
    // 实现分享功能
  }

  void _reportMoment(moment) {
    // 实现举报功能
  }

  void _navigateToFishingSpot(int? fishingSpotId) {
    if (fishingSpotId != null) {
      context.pushNamed(
        'fishing_spot_detail',
        pathParameters: {'id': fishingSpotId.toString()},
      );
    }
  }

  void _searchByTag(String tag) {
    setState(() {
      if (!_selectedTags.contains(tag)) {
        _selectedTags.add(tag);
      }
    });
    _loadData(refresh: true);
  }

  bool _isOwnMoment(moment) {
    final authViewModel = context.read<AuthViewModel>();
    return authViewModel.currentUser?.id == moment.publisher.id;
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';

    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  bool isAuthenticated() {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    return authViewModel.isUserLoggedIn();
  }
}

// 优化的筛选栏代理
class _OptimizedFilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _OptimizedFilterBarDelegate({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: overlapsContent ? 4 : 0,
      child: child,
    );
  }

  @override
  double get maxExtent => 84;

  @override
  double get minExtent => 48;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
