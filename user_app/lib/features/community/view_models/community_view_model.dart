import 'package:flutter/material.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/constants/vote_type.dart';

class CommunityViewModel extends ChangeNotifier {
  final MomentService _momentService;

  CommunityViewModel(this._momentService);

  // State management
  List<MomentVo> _moments = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  int _currentPage = 1;
  static const int _pageSize = 10;

  // Filters
  String _selectedMomentType = "全部";
  List<String> _selectedTags = [];
  String _searchQuery = "";

  // Filter options similar to fishing spots
  final List<String> _momentTypeFilters = [
    "全部",
    "钓获分享",
    "装备展示",
    "技巧分享",
    "问答求助"
  ];

  // Getters
  List<MomentVo> get moments => _moments;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get errorMessage => _errorMessage;
  String get selectedMomentType => _selectedMomentType;
  List<String> get selectedTags => _selectedTags;
  String get searchQuery => _searchQuery;
  List<String> get momentTypeFilters => _momentTypeFilters;

  // Convert filter type to API moment type
  String? _getApiMomentType(String filterType) {
    switch (filterType) {
      case "钓获分享":
        return "fishing_catch";
      case "装备展示":
        return "equipment";
      case "技巧分享":
        return "technique";
      case "问答求助":
        return "question";
      default:
        return null; // "全部" case
    }
  }

  Future<void> loadMoments({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _moments.clear();
    }

    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final request = MomentListRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        tag: _selectedTags.isNotEmpty ? _selectedTags.first : null,
      );

      final response = await _momentService.getMoments(request);

      if (refresh) {
        _moments = response.records;
      } else {
        _moments.addAll(response.records);
      }

      _hasMore = _currentPage < response.pages;
      _currentPage++;
      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
      print('Error loading moments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> searchMoments(String query) async {
    _searchQuery = query;
    await loadMoments(refresh: true);
  }

  void setMomentTypeFilter(String momentType) {
    _selectedMomentType = momentType;
    loadMoments(refresh: true);
  }

  void toggleTag(String tag) {
    if (_selectedTags.contains(tag)) {
      _selectedTags.remove(tag);
    } else {
      _selectedTags.add(tag);
    }
    loadMoments(refresh: true);
  }

  void clearAllFilters() {
    _selectedMomentType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    loadMoments(refresh: true);
  }

  // Like/unlike functionality
  Future<void> toggleLikeMoment(int momentId) async {
    try {
      final momentIndex = _moments.indexWhere((m) => m.id == momentId);
      if (momentIndex == -1) return;

      final moment = _moments[momentIndex];
      final wasLiked = moment.liked ?? false;

      // Optimistic update
      final updatedMoment = moment.copyWith(
        liked: !wasLiked,
        likeCount: (moment.likeCount ?? 0) + (wasLiked ? -1 : 1),
      );
      _moments[momentIndex] = updatedMoment;
      notifyListeners();

      // API call
      await _momentService.voteMoment(
        momentId,
        wasLiked ? VoteType.downVote : VoteType.upVote,
      );
    } catch (e) {
      // Revert on error
      loadMoments(refresh: true);
      print('Error toggling like: $e');
    }
  }

  // Follow/unfollow user
  Future<void> toggleFollowUser(int userId) async {
    try {
      // Find moments by this user and update follow status
      for (int i = 0; i < _moments.length; i++) {
        if (_moments[i].publisher.id == userId) {
          final moment = _moments[i];
          final wasFollowed = moment.followed ?? false;

          final updatedMoment = moment.copyWith(
            followed: !wasFollowed,
            followCount: moment.followCount + (wasFollowed ? -1 : 1),
          );
          _moments[i] = updatedMoment;
        }
      }
      notifyListeners();

      // API call would go here
      // await _userService.toggleFollow(userId);
    } catch (e) {
      // Revert on error
      loadMoments(refresh: true);
      print('Error toggling follow: $e');
    }
  }

  int getActiveFilterCount() {
    int count = 0;
    if (_selectedMomentType != "全部") count++;
    if (_selectedTags.isNotEmpty) count += _selectedTags.length;
    return count;
  }

  void reset() {
    _moments.clear();
    _currentPage = 0;
    _hasMore = true;
    _isLoading = false;
    _errorMessage = null;
    _selectedMomentType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    notifyListeners();
  }
}
