import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/tips/view_models/tips_view_model.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class TipsPage extends StatefulWidget {
  const TipsPage({super.key});

  @override
  State<TipsPage> createState() => _TipsPageState();
}

class _TipsPageState extends State<TipsPage> {
  final ScrollController _scrollController = ScrollController();
  late TipsViewModel _tipsViewModel;

  String _selectedTab = "活动";
  final List<String> _tabOptions = ["活动", "通知", "公告"];

  String _selectedFilter = "全部";
  final List<String> _filterOptions = ["全部", "进行中", "即将开始", "已结束"];

  @override
  void initState() {
    super.initState();
    _tipsViewModel = context.read<TipsViewModel>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initialLoad() async {
    await _loadData(refresh: true);
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_tipsViewModel.isLoading && _tipsViewModel.hasMore) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (_tipsViewModel.isLoading && !refresh) return;

    try {
      await _tipsViewModel.loadTips(refresh: refresh);
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) setState(() {});
    }
  }

  Future<void> _loadMoreData() async {
    if (_tipsViewModel.isLoading || !_tipsViewModel.hasMore) return;

    try {
      await _tipsViewModel.loadTips();
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final tipsViewModel = context.watch<TipsViewModel>();
    final isLoading = tipsViewModel.isLoading;
    final tips = tipsViewModel.tips;
    final hasMore = tipsViewModel.hasMore;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          // App Bar
          SliverAppBar(
            floating: true,
            backgroundColor: Colors.white,
            elevation: 0,
            title: const Text(
              '活动中心',
              style: TextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
            actions: [
              IconButton(
                onPressed: _showCalendarView,
                icon: Icon(Icons.calendar_today, color: Colors.grey.shade700),
              ),
              Stack(
                children: [
                  IconButton(
                    onPressed: _showNotificationSettings,
                    icon: Icon(Icons.notifications_outlined,
                        color: Colors.grey.shade700),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          // Banner Section - 重要活动轮播
          SliverToBoxAdapter(
            child: Container(
              height: 180,
              color: Colors.white,
              child: PageView.builder(
                itemCount: 3,
                itemBuilder: (context, index) {
                  return _buildBannerCard(index);
                },
              ),
            ),
          ),

          // Tab Bar
          SliverPersistentHeader(
            pinned: true,
            delegate: _TabBarDelegate(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // Tab selector
                    Container(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                      child: Row(
                        children: _tabOptions.map((tab) {
                          final isSelected = _selectedTab == tab;
                          return Expanded(
                            child: GestureDetector(
                              onTap: () {
                                HapticFeedback.selectionClick();
                                setState(() => _selectedTab = tab);
                                _loadData(refresh: true);
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: isSelected
                                          ? Theme.of(context).primaryColor
                                          : Colors.transparent,
                                      width: 2,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      _getTabIcon(tab),
                                      size: 16,
                                      color: isSelected
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey.shade600,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      tab,
                                      style: TextStyle(
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey.shade600,
                                        fontWeight: isSelected
                                            ? FontWeight.w600
                                            : FontWeight.w500,
                                        fontSize: 15,
                                      ),
                                    ),
                                    if (_getTabCount(tab) > 0) ...[
                                      const SizedBox(width: 4),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Theme.of(context).primaryColor
                                              : Colors.grey.shade300,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: Text(
                                          _getTabCount(tab).toString(),
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.grey.shade600,
                                            fontSize: 11,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    // Filter bar
                    if (_selectedTab == "活动")
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                        child: _buildFilterBar(),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // Content
          if (isLoading && tips.isEmpty)
            SliverToBoxAdapter(
              child: Container(
                height: MediaQuery.of(context).size.height * 0.5,
                alignment: Alignment.center,
                child: const CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            _buildContentList(tips, isLoading, hasMore),
        ],
      ),
    );
  }

  Widget _buildBannerCard(int index) {
    final banners = [
      {
        'title': '2024钓王争霸赛',
        'subtitle': '总奖金10万元，等你来战！',
        'date': '12月15日-12月20日',
        'gradient': [Colors.blue.shade600, Colors.blue.shade400],
        'icon': Icons.emoji_events,
      },
      {
        'title': '冬季路亚交流会',
        'subtitle': '资深钓手现场教学',
        'date': '12月8日 14:00',
        'gradient': [Colors.purple.shade600, Colors.purple.shade400],
        'icon': Icons.school,
      },
      {
        'title': '年终装备特惠',
        'subtitle': '全场装备8折起',
        'date': '即日起至12月31日',
        'gradient': [Colors.orange.shade600, Colors.orange.shade400],
        'icon': Icons.local_offer,
      },
    ];

    final banner = banners[index];

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: banner['gradient'] as List<Color>,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (banner['gradient'] as List<Color>)[0].withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: -30,
            bottom: -30,
            child: Icon(
              banner['icon'] as IconData,
              size: 120,
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '热门活动',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  banner['title'] as String,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  banner['subtitle'] as String,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.white.withOpacity(0.8),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      banner['date'] as String,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _filterOptions.map((filter) {
          final isSelected = _selectedFilter == filter;
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                setState(() => _selectedFilter = filter);
                _loadData(refresh: true);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  filter,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildContentList(tips, bool isLoading, bool hasMore) {
    if (tips.isEmpty && !isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getEmptyIcon(),
                  size: 40,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _getEmptyMessage(),
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _getEmptySubMessage(),
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == tips.length) {
            if (isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              );
            } else if (!hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: Text(
                    '没有更多了',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 13,
                    ),
                  ),
                ),
              );
            }
            return const SizedBox(height: 24);
          }

          if (_selectedTab == "活动") {
            return _buildActivityCard(tips[index]);
          } else if (_selectedTab == "通知") {
            return _buildNotificationCard(tips[index]);
          } else {
            return _buildAnnouncementCard(tips[index]);
          }
        },
        childCount: tips.isEmpty ? 0 : tips.length + 1,
      ),
    );
  }

  Widget _buildActivityCard(activity) {
    final bool isOngoing = _isActivityOngoing(activity);
    final bool isUpcoming = _isActivityUpcoming(activity);
    final int participantCount = activity.participantCount ?? 0;
    final int maxParticipants = activity.maxParticipants ?? 0;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showActivityDetail(activity),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Activity Image
            if (activity.coverImage != null)
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(12),
                    ),
                    child: AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Image.network(
                        activity.coverImage!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.event,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  // Status badge
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(activity).withOpacity(0.9),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getStatusIcon(activity),
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getStatusText(activity),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Activity type
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          activity.type ?? '官方活动',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (activity.isPremium)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.amber, Colors.orange],
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                size: 10,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                'VIP',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Title
                  Text(
                    activity.title ?? '活动标题',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Description
                  if (activity.description != null)
                    Text(
                      activity.description!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const SizedBox(height: 16),

                  // Info rows
                  _buildInfoRow(
                    Icons.calendar_today,
                    '活动时间',
                    _formatActivityTime(activity),
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                    Icons.location_on,
                    '活动地点',
                    activity.location ?? '线上活动',
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                    Icons.people,
                    '参与人数',
                    maxParticipants > 0
                        ? '$participantCount / $maxParticipants 人'
                        : '$participantCount 人已报名',
                  ),

                  const SizedBox(height: 16),

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: isOngoing || isUpcoming
                          ? () => _joinActivity(activity)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isOngoing
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        _getActionButtonText(activity),
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(notification) {
    final bool isRead = notification.isRead ?? false;
    final bool isImportant = notification.isImportant ?? false;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: isRead ? Colors.white : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: isImportant
            ? Border.all(
                color: Colors.orange,
                width: 2,
              )
            : null,
      ),
      child: InkWell(
        onTap: () => _markAsRead(notification),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color:
                      _getNotificationColor(notification.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        if (isImportant)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '重要',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        Expanded(
                          child: Text(
                            notification.title ?? '系统通知',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color:
                                  isRead ? Colors.grey.shade800 : Colors.black,
                            ),
                          ),
                        ),
                        if (!isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.content ?? '',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _formatTimeAgo(notification.createdAt),
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnnouncementCard(announcement) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showAnnouncementDetail(announcement),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.campaign,
                          size: 12,
                          color: Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '官方公告',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(announcement.publishedAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Title
              Text(
                announcement.title ?? '公告标题',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Content preview
              Text(
                announcement.summary ?? announcement.content ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Read more
              Row(
                children: [
                  Text(
                    '查看详情',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade500,
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // Helper methods
  IconData _getTabIcon(String tab) {
    switch (tab) {
      case '活动':
        return Icons.event;
      case '通知':
        return Icons.notifications;
      case '公告':
        return Icons.campaign;
      default:
        return Icons.info;
    }
  }

  int _getTabCount(String tab) {
    switch (tab) {
      case '活动':
        return 3; // 进行中的活动数
      case '通知':
        return 5; // 未读通知数
      case '公告':
        return 0; // 公告一般不显示数量
      default:
        return 0;
    }
  }

  IconData _getEmptyIcon() {
    switch (_selectedTab) {
      case '活动':
        return Icons.event_busy;
      case '通知':
        return Icons.notifications_none;
      case '公告':
        return Icons.speaker_notes_off;
      default:
        return Icons.inbox;
    }
  }

  String _getEmptyMessage() {
    switch (_selectedTab) {
      case '活动':
        return '暂无活动';
      case '通知':
        return '暂无通知';
      case '公告':
        return '暂无公告';
      default:
        return '暂无内容';
    }
  }

  String _getEmptySubMessage() {
    switch (_selectedTab) {
      case '活动':
        return '精彩活动即将上线';
      case '通知':
        return '所有通知已读';
      case '公告':
        return '暂无最新公告';
      default:
        return '';
    }
  }

  Color _getStatusColor(activity) {
    if (_isActivityOngoing(activity)) return Colors.green;
    if (_isActivityUpcoming(activity)) return Colors.blue;
    return Colors.grey;
  }

  IconData _getStatusIcon(activity) {
    if (_isActivityOngoing(activity)) return Icons.play_circle;
    if (_isActivityUpcoming(activity)) return Icons.schedule;
    return Icons.check_circle;
  }

  String _getStatusText(activity) {
    if (_isActivityOngoing(activity)) return '进行中';
    if (_isActivityUpcoming(activity)) return '即将开始';
    return '已结束';
  }

  String _getActionButtonText(activity) {
    if (_isActivityOngoing(activity)) return '立即参与';
    if (_isActivityUpcoming(activity)) return '立即报名';
    return '查看详情';
  }

  bool _isActivityOngoing(activity) {
    // 实际实现需要根据活动时间判断
    return activity.status == 'ongoing';
  }

  bool _isActivityUpcoming(activity) {
    // 实际实现需要根据活动时间判断
    return activity.status == 'upcoming';
  }

  String _formatActivityTime(activity) {
    // 格式化活动时间
    return '12月15日 14:00 - 16:00';
  }

  Color _getNotificationColor(String? type) {
    switch (type) {
      case 'system':
        return Colors.blue;
      case 'activity':
        return Colors.green;
      case 'award':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(String? type) {
    switch (type) {
      case 'system':
        return Icons.info;
      case 'activity':
        return Icons.event;
      case 'award':
        return Icons.emoji_events;
      default:
        return Icons.notifications;
    }
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';

    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }

  String _formatDate(String? date) {
    if (date == null) return '';

    try {
      final dateTime = DateTime.parse(date);
      return '${dateTime.month}月${dateTime.day}日';
    } catch (e) {
      return date;
    }
  }

  // Actions
  void _showCalendarView() {
    // Navigate to calendar view
  }

  void _showNotificationSettings() {
    // Navigate to notification settings
  }

  void _showActivityDetail(activity) {
    // Navigate to activity detail
  }

  void _joinActivity(activity) {
    if (!isAuthenticated()) {
      _showLoginDialog('参与活动');
      return;
    }
    // Join activity logic
  }

  void _markAsRead(notification) {
    // Mark notification as read
  }

  void _showAnnouncementDetail(announcement) {
    // Navigate to announcement detail
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  bool isAuthenticated() {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    return authViewModel.isUserLoggedIn();
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _TabBarDelegate({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 110;

  @override
  double get minExtent => 110;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
