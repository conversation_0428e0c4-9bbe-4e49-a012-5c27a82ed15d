import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';

class BookmarkApi extends BaseApi {
  static const String bookmarkPath = '/bookmarks';

  BookmarkApi(super.dio);

  /// Bookmark a moment
  Future<void> bookmarkMoment(int momentId) async {
    final response = await safeApiCall(
      () => dio.post('$bookmarkPath/moment/$momentId'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Remove bookmark from a moment
  Future<void> unbookmarkMoment(int momentId) async {
    final response = await safeApiCall(
      () => dio.delete('$bookmarkPath/moment/$momentId'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Check if a moment is bookmarked
  Future<bool> isBookmarked(int momentId) async {
    final response = await safeApiCall(
      () => dio.get('$bookmarkPath/moment/$momentId/status'),
      (data) => data['bookmarked'] as bool? ?? false,
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? false;
  }
}
