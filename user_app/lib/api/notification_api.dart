import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/notification/notification_vo.dart';

class NotificationApi extends BaseApi {
  static const String notificationPath = '/notifications';

  NotificationApi(super.dio);

  /// Get user notifications
  Future<List<NotificationVo>> getNotifications({
    int page = 1,
    int size = 20,
    bool? unreadOnly,
  }) async {
    final response = await safeApiCall(
      () => dio.get(notificationPath, queryParameters: {
        'page': page,
        'size': size,
        if (unreadOnly != null) 'unreadOnly': unreadOnly,
      }),
      (data) {
        if (data is List) {
          return data.map((item) => NotificationVo.fromMap(item)).toList();
        }
        return <NotificationVo>[];
      },
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? [];
  }

  /// Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    final response = await safeApiCall(
      () => dio.put('$notificationPath/$notificationId/read'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    final response = await safeApiCall(
      () => dio.put('$notificationPath/read-all'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Get unread notification count
  Future<int> getUnreadCount() async {
    final response = await safeApiCall(
      () => dio.get('$notificationPath/unread-count'),
      (data) => data['count'] as int? ?? 0,
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? 0;
  }

  /// Delete notification
  Future<void> deleteNotification(int notificationId) async {
    final response = await safeApiCall(
      () => dio.delete('$notificationPath/$notificationId'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }
}
