import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';

class ReportApi extends BaseApi {
  static const String reportPath = '/reports';

  ReportApi(super.dio);

  /// Report a moment
  Future<void> reportMoment(int momentId, String reason, {String? description}) async {
    final response = await safeApiCall(
      () => dio.post('$reportPath/moment', data: {
        'momentId': momentId,
        'reason': reason,
        'description': description,
      }),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Report a user
  Future<void> reportUser(int userId, String reason, {String? description}) async {
    final response = await safeApiCall(
      () => dio.post('$reportPath/user', data: {
        'userId': userId,
        'reason': reason,
        'description': description,
      }),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  /// Get report reasons
  Future<List<String>> getReportReasons() async {
    final response = await safeApiCall(
      () => dio.get('$reportPath/reasons'),
      (data) => List<String>.from(data['reasons'] ?? []),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? [
      '垃圾信息',
      '违法违规',
      '色情内容',
      '暴力内容',
      '虚假信息',
      '侵犯版权',
      '其他'
    ];
  }
}
