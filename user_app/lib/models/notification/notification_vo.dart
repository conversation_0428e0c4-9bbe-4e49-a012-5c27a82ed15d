import 'package:dart_mappable/dart_mappable.dart';

part 'notification_vo.mapper.dart';

@MappableClass()
class NotificationVo with NotificationVoMappable {
  final int id;
  final int userId;
  final String type; // like, comment, follow, system
  final String title;
  final String content;
  final bool isRead;
  final String? relatedType; // moment, user, system
  final int? relatedId;
  final String? imageUrl;
  final String createdAt;

  const NotificationVo({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.content,
    required this.isRead,
    this.relatedType,
    this.relatedId,
    this.imageUrl,
    required this.createdAt,
  });

  static final fromMap = NotificationVoMapper.fromMap;
}
