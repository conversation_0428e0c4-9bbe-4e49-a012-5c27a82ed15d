// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'notification_vo.dart';

class NotificationVoMapper extends ClassMapperBase<NotificationVo> {
  NotificationVoMapper._();

  static NotificationVoMapper? _instance;
  static NotificationVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = NotificationVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'NotificationVo';

  static int _$id(NotificationVo v) => v.id;
  static const Field<NotificationVo, int> _f$id = Field('id', _$id);
  static int _$userId(NotificationVo v) => v.userId;
  static const Field<NotificationVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static String _$type(NotificationVo v) => v.type;
  static const Field<NotificationVo, String> _f$type = Field('type', _$type);
  static String _$title(NotificationVo v) => v.title;
  static const Field<NotificationVo, String> _f$title = Field('title', _$title);
  static String _$content(NotificationVo v) => v.content;
  static const Field<NotificationVo, String> _f$content =
      Field('content', _$content);
  static bool _$isRead(NotificationVo v) => v.isRead;
  static const Field<NotificationVo, bool> _f$isRead =
      Field('isRead', _$isRead, key: r'is_read');
  static String? _$relatedType(NotificationVo v) => v.relatedType;
  static const Field<NotificationVo, String> _f$relatedType =
      Field('relatedType', _$relatedType, key: r'related_type', opt: true);
  static int? _$relatedId(NotificationVo v) => v.relatedId;
  static const Field<NotificationVo, int> _f$relatedId =
      Field('relatedId', _$relatedId, key: r'related_id', opt: true);
  static String? _$imageUrl(NotificationVo v) => v.imageUrl;
  static const Field<NotificationVo, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url', opt: true);
  static String _$createdAt(NotificationVo v) => v.createdAt;
  static const Field<NotificationVo, String> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at');

  @override
  final MappableFields<NotificationVo> fields = const {
    #id: _f$id,
    #userId: _f$userId,
    #type: _f$type,
    #title: _f$title,
    #content: _f$content,
    #isRead: _f$isRead,
    #relatedType: _f$relatedType,
    #relatedId: _f$relatedId,
    #imageUrl: _f$imageUrl,
    #createdAt: _f$createdAt,
  };

  static NotificationVo _instantiate(DecodingData data) {
    return NotificationVo(
        id: data.dec(_f$id),
        userId: data.dec(_f$userId),
        type: data.dec(_f$type),
        title: data.dec(_f$title),
        content: data.dec(_f$content),
        isRead: data.dec(_f$isRead),
        relatedType: data.dec(_f$relatedType),
        relatedId: data.dec(_f$relatedId),
        imageUrl: data.dec(_f$imageUrl),
        createdAt: data.dec(_f$createdAt));
  }

  @override
  final Function instantiate = _instantiate;

  static NotificationVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<NotificationVo>(map);
  }

  static NotificationVo fromJson(String json) {
    return ensureInitialized().decodeJson<NotificationVo>(json);
  }
}

mixin NotificationVoMappable {
  String toJson() {
    return NotificationVoMapper.ensureInitialized()
        .encodeJson<NotificationVo>(this as NotificationVo);
  }

  Map<String, dynamic> toMap() {
    return NotificationVoMapper.ensureInitialized()
        .encodeMap<NotificationVo>(this as NotificationVo);
  }

  NotificationVoCopyWith<NotificationVo, NotificationVo, NotificationVo>
      get copyWith =>
          _NotificationVoCopyWithImpl<NotificationVo, NotificationVo>(
              this as NotificationVo, $identity, $identity);
  @override
  String toString() {
    return NotificationVoMapper.ensureInitialized()
        .stringifyValue(this as NotificationVo);
  }

  @override
  bool operator ==(Object other) {
    return NotificationVoMapper.ensureInitialized()
        .equalsValue(this as NotificationVo, other);
  }

  @override
  int get hashCode {
    return NotificationVoMapper.ensureInitialized()
        .hashValue(this as NotificationVo);
  }
}

extension NotificationVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, NotificationVo, $Out> {
  NotificationVoCopyWith<$R, NotificationVo, $Out> get $asNotificationVo =>
      $base.as((v, t, t2) => _NotificationVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class NotificationVoCopyWith<$R, $In extends NotificationVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      int? userId,
      String? type,
      String? title,
      String? content,
      bool? isRead,
      String? relatedType,
      int? relatedId,
      String? imageUrl,
      String? createdAt});
  NotificationVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _NotificationVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, NotificationVo, $Out>
    implements NotificationVoCopyWith<$R, NotificationVo, $Out> {
  _NotificationVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<NotificationVo> $mapper =
      NotificationVoMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          int? userId,
          String? type,
          String? title,
          String? content,
          bool? isRead,
          Object? relatedType = $none,
          Object? relatedId = $none,
          Object? imageUrl = $none,
          String? createdAt}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (userId != null) #userId: userId,
        if (type != null) #type: type,
        if (title != null) #title: title,
        if (content != null) #content: content,
        if (isRead != null) #isRead: isRead,
        if (relatedType != $none) #relatedType: relatedType,
        if (relatedId != $none) #relatedId: relatedId,
        if (imageUrl != $none) #imageUrl: imageUrl,
        if (createdAt != null) #createdAt: createdAt
      }));
  @override
  NotificationVo $make(CopyWithData data) => NotificationVo(
      id: data.get(#id, or: $value.id),
      userId: data.get(#userId, or: $value.userId),
      type: data.get(#type, or: $value.type),
      title: data.get(#title, or: $value.title),
      content: data.get(#content, or: $value.content),
      isRead: data.get(#isRead, or: $value.isRead),
      relatedType: data.get(#relatedType, or: $value.relatedType),
      relatedId: data.get(#relatedId, or: $value.relatedId),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      createdAt: data.get(#createdAt, or: $value.createdAt));

  @override
  NotificationVoCopyWith<$R2, NotificationVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _NotificationVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
