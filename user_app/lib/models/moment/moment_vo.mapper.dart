// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'moment_vo.dart';

class MomentImageVoMapper extends ClassMapperBase<MomentImageVo> {
  MomentImageVoMapper._();

  static MomentImageVoMapper? _instance;
  static MomentImageVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentImageVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'MomentImageVo';

  static int _$id(MomentImageVo v) => v.id;
  static const Field<MomentImageVo, int> _f$id = Field('id', _$id);
  static int _$momentId(MomentImageVo v) => v.momentId;
  static const Field<MomentImageVo, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id');
  static String _$imageUrl(MomentImageVo v) => v.imageUrl;
  static const Field<MomentImageVo, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url');
  static int _$displayOrder(MomentImageVo v) => v.displayOrder;
  static const Field<MomentImageVo, int> _f$displayOrder =
      Field('displayOrder', _$displayOrder, key: r'display_order');

  @override
  final MappableFields<MomentImageVo> fields = const {
    #id: _f$id,
    #momentId: _f$momentId,
    #imageUrl: _f$imageUrl,
    #displayOrder: _f$displayOrder,
  };

  static MomentImageVo _instantiate(DecodingData data) {
    return MomentImageVo(
        id: data.dec(_f$id),
        momentId: data.dec(_f$momentId),
        imageUrl: data.dec(_f$imageUrl),
        displayOrder: data.dec(_f$displayOrder));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentImageVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentImageVo>(map);
  }

  static MomentImageVo fromJson(String json) {
    return ensureInitialized().decodeJson<MomentImageVo>(json);
  }
}

mixin MomentImageVoMappable {
  String toJson() {
    return MomentImageVoMapper.ensureInitialized()
        .encodeJson<MomentImageVo>(this as MomentImageVo);
  }

  Map<String, dynamic> toMap() {
    return MomentImageVoMapper.ensureInitialized()
        .encodeMap<MomentImageVo>(this as MomentImageVo);
  }

  MomentImageVoCopyWith<MomentImageVo, MomentImageVo, MomentImageVo>
      get copyWith => _MomentImageVoCopyWithImpl<MomentImageVo, MomentImageVo>(
          this as MomentImageVo, $identity, $identity);
  @override
  String toString() {
    return MomentImageVoMapper.ensureInitialized()
        .stringifyValue(this as MomentImageVo);
  }

  @override
  bool operator ==(Object other) {
    return MomentImageVoMapper.ensureInitialized()
        .equalsValue(this as MomentImageVo, other);
  }

  @override
  int get hashCode {
    return MomentImageVoMapper.ensureInitialized()
        .hashValue(this as MomentImageVo);
  }
}

extension MomentImageVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, MomentImageVo, $Out> {
  MomentImageVoCopyWith<$R, MomentImageVo, $Out> get $asMomentImageVo =>
      $base.as((v, t, t2) => _MomentImageVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentImageVoCopyWith<$R, $In extends MomentImageVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({int? id, int? momentId, String? imageUrl, int? displayOrder});
  MomentImageVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _MomentImageVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentImageVo, $Out>
    implements MomentImageVoCopyWith<$R, MomentImageVo, $Out> {
  _MomentImageVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentImageVo> $mapper =
      MomentImageVoMapper.ensureInitialized();
  @override
  $R call({int? id, int? momentId, String? imageUrl, int? displayOrder}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (momentId != null) #momentId: momentId,
        if (imageUrl != null) #imageUrl: imageUrl,
        if (displayOrder != null) #displayOrder: displayOrder
      }));
  @override
  MomentImageVo $make(CopyWithData data) => MomentImageVo(
      id: data.get(#id, or: $value.id),
      momentId: data.get(#momentId, or: $value.momentId),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      displayOrder: data.get(#displayOrder, or: $value.displayOrder));

  @override
  MomentImageVoCopyWith<$R2, MomentImageVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentImageVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class MomentVoMapper extends ClassMapperBase<MomentVo> {
  MomentVoMapper._();

  static MomentVoMapper? _instance;
  static MomentVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentVoMapper._());
      MomentImageVoMapper.ensureInitialized();
      UserMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'MomentVo';

  static int _$id(MomentVo v) => v.id;
  static const Field<MomentVo, int> _f$id = Field('id', _$id);
  static int? _$userId(MomentVo v) => v.userId;
  static const Field<MomentVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id', opt: true);
  static String? _$content(MomentVo v) => v.content;
  static const Field<MomentVo, String> _f$content =
      Field('content', _$content, opt: true);
  static List<MomentImageVo>? _$images(MomentVo v) => v.images;
  static const Field<MomentVo, List<MomentImageVo>> _f$images =
      Field('images', _$images, opt: true);
  static String? _$momentType(MomentVo v) => v.momentType;
  static const Field<MomentVo, String> _f$momentType =
      Field('momentType', _$momentType, key: r'moment_type', opt: true);
  static String? _$typeSpecificData(MomentVo v) => v.typeSpecificData;
  static const Field<MomentVo, String> _f$typeSpecificData = Field(
      'typeSpecificData', _$typeSpecificData,
      key: r'type_specific_data', opt: true);
  static String? _$tag(MomentVo v) => v.tag;
  static const Field<MomentVo, String> _f$tag = Field('tag', _$tag, opt: true);
  static num? _$fishCatch(MomentVo v) => v.fishCatch;
  static const Field<MomentVo, num> _f$fishCatch =
      Field('fishCatch', _$fishCatch, key: r'fish_catch', opt: true);
  static int? _$fishingSpotId(MomentVo v) => v.fishingSpotId;
  static const Field<MomentVo, int> _f$fishingSpotId = Field(
      'fishingSpotId', _$fishingSpotId,
      key: r'fishing_spot_id', opt: true);
  static String? _$fishingSpotName(MomentVo v) => v.fishingSpotName;
  static const Field<MomentVo, String> _f$fishingSpotName = Field(
      'fishingSpotName', _$fishingSpotName,
      key: r'fishing_spot_name', opt: true);
  static double? _$longitude(MomentVo v) => v.longitude;
  static const Field<MomentVo, double> _f$longitude =
      Field('longitude', _$longitude, opt: true);
  static double? _$latitude(MomentVo v) => v.latitude;
  static const Field<MomentVo, double> _f$latitude =
      Field('latitude', _$latitude, opt: true);
  static String? _$province(MomentVo v) => v.province;
  static const Field<MomentVo, String> _f$province =
      Field('province', _$province, opt: true);
  static String? _$city(MomentVo v) => v.city;
  static const Field<MomentVo, String> _f$city =
      Field('city', _$city, opt: true);
  static String? _$county(MomentVo v) => v.county;
  static const Field<MomentVo, String> _f$county =
      Field('county', _$county, opt: true);
  static String? _$addressDetail(MomentVo v) => v.addressDetail;
  static const Field<MomentVo, String> _f$addressDetail = Field(
      'addressDetail', _$addressDetail,
      key: r'address_detail', opt: true);
  static int? _$likeCount(MomentVo v) => v.likeCount;
  static const Field<MomentVo, int> _f$likeCount =
      Field('likeCount', _$likeCount, key: r'like_count', opt: true);
  static int _$numberOfDislikes(MomentVo v) => v.numberOfDislikes;
  static const Field<MomentVo, int> _f$numberOfDislikes = Field(
      'numberOfDislikes', _$numberOfDislikes,
      key: r'number_of_dislikes', opt: true, def: 0);
  static int? _$status(MomentVo v) => v.status;
  static const Field<MomentVo, int> _f$status =
      Field('status', _$status, opt: true);
  static int? _$follows(MomentVo v) => v.follows;
  static const Field<MomentVo, int> _f$follows =
      Field('follows', _$follows, opt: true);
  static bool? _$followed(MomentVo v) => v.followed;
  static const Field<MomentVo, bool> _f$followed =
      Field('followed', _$followed, opt: true);
  static bool? _$liked(MomentVo v) => v.liked;
  static const Field<MomentVo, bool> _f$liked =
      Field('liked', _$liked, opt: true);
  static bool? _$disliked(MomentVo v) => v.disliked;
  static const Field<MomentVo, bool> _f$disliked =
      Field('disliked', _$disliked, opt: true);
  static String? _$createdAt(MomentVo v) => v.createdAt;
  static const Field<MomentVo, String> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at', opt: true);
  static String? _$updateTime(MomentVo v) => v.updateTime;
  static const Field<MomentVo, String> _f$updateTime =
      Field('updateTime', _$updateTime, key: r'update_time', opt: true);
  static User _$publisher(MomentVo v) => v.publisher;
  static const Field<MomentVo, User> _f$publisher =
      Field('publisher', _$publisher);
  static num _$numberOfComments(MomentVo v) => v.numberOfComments;
  static const Field<MomentVo, num> _f$numberOfComments = Field(
      'numberOfComments', _$numberOfComments,
      key: r'number_of_comments', opt: true, def: 0);
  static num _$followCount(MomentVo v) => v.followCount;
  static const Field<MomentVo, num> _f$followCount = Field(
      'followCount', _$followCount,
      key: r'follow_count', opt: true, def: 0);
  static num _$attentionCount(MomentVo v) => v.attentionCount;
  static const Field<MomentVo, num> _f$attentionCount = Field(
      'attentionCount', _$attentionCount,
      key: r'attention_count', opt: true, def: 0);
  static num _$momentCount(MomentVo v) => v.momentCount;
  static const Field<MomentVo, num> _f$momentCount = Field(
      'momentCount', _$momentCount,
      key: r'moment_count', opt: true, def: 0);
  static num _$commentCount(MomentVo v) => v.commentCount;
  static const Field<MomentVo, num> _f$commentCount = Field(
      'commentCount', _$commentCount,
      key: r'comment_count', opt: true, def: 0);
  static num _$viewCount(MomentVo v) => v.viewCount;
  static const Field<MomentVo, num> _f$viewCount =
      Field('viewCount', _$viewCount, key: r'view_count', opt: true, def: 0);

  @override
  final MappableFields<MomentVo> fields = const {
    #id: _f$id,
    #userId: _f$userId,
    #content: _f$content,
    #images: _f$images,
    #momentType: _f$momentType,
    #typeSpecificData: _f$typeSpecificData,
    #tag: _f$tag,
    #fishCatch: _f$fishCatch,
    #fishingSpotId: _f$fishingSpotId,
    #fishingSpotName: _f$fishingSpotName,
    #longitude: _f$longitude,
    #latitude: _f$latitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #addressDetail: _f$addressDetail,
    #likeCount: _f$likeCount,
    #numberOfDislikes: _f$numberOfDislikes,
    #status: _f$status,
    #follows: _f$follows,
    #followed: _f$followed,
    #liked: _f$liked,
    #disliked: _f$disliked,
    #createdAt: _f$createdAt,
    #updateTime: _f$updateTime,
    #publisher: _f$publisher,
    #numberOfComments: _f$numberOfComments,
    #followCount: _f$followCount,
    #attentionCount: _f$attentionCount,
    #momentCount: _f$momentCount,
    #commentCount: _f$commentCount,
    #viewCount: _f$viewCount,
  };

  static MomentVo _instantiate(DecodingData data) {
    return MomentVo(
        id: data.dec(_f$id),
        userId: data.dec(_f$userId),
        content: data.dec(_f$content),
        images: data.dec(_f$images),
        momentType: data.dec(_f$momentType),
        typeSpecificData: data.dec(_f$typeSpecificData),
        tag: data.dec(_f$tag),
        fishCatch: data.dec(_f$fishCatch),
        fishingSpotId: data.dec(_f$fishingSpotId),
        fishingSpotName: data.dec(_f$fishingSpotName),
        longitude: data.dec(_f$longitude),
        latitude: data.dec(_f$latitude),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        addressDetail: data.dec(_f$addressDetail),
        likeCount: data.dec(_f$likeCount),
        numberOfDislikes: data.dec(_f$numberOfDislikes),
        status: data.dec(_f$status),
        follows: data.dec(_f$follows),
        followed: data.dec(_f$followed),
        liked: data.dec(_f$liked),
        disliked: data.dec(_f$disliked),
        createdAt: data.dec(_f$createdAt),
        updateTime: data.dec(_f$updateTime),
        publisher: data.dec(_f$publisher),
        numberOfComments: data.dec(_f$numberOfComments),
        followCount: data.dec(_f$followCount),
        attentionCount: data.dec(_f$attentionCount),
        momentCount: data.dec(_f$momentCount),
        commentCount: data.dec(_f$commentCount),
        viewCount: data.dec(_f$viewCount));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentVo>(map);
  }

  static MomentVo fromJson(String json) {
    return ensureInitialized().decodeJson<MomentVo>(json);
  }
}

mixin MomentVoMappable {
  String toJson() {
    return MomentVoMapper.ensureInitialized()
        .encodeJson<MomentVo>(this as MomentVo);
  }

  Map<String, dynamic> toMap() {
    return MomentVoMapper.ensureInitialized()
        .encodeMap<MomentVo>(this as MomentVo);
  }

  MomentVoCopyWith<MomentVo, MomentVo, MomentVo> get copyWith =>
      _MomentVoCopyWithImpl<MomentVo, MomentVo>(
          this as MomentVo, $identity, $identity);
  @override
  String toString() {
    return MomentVoMapper.ensureInitialized().stringifyValue(this as MomentVo);
  }

  @override
  bool operator ==(Object other) {
    return MomentVoMapper.ensureInitialized()
        .equalsValue(this as MomentVo, other);
  }

  @override
  int get hashCode {
    return MomentVoMapper.ensureInitialized().hashValue(this as MomentVo);
  }
}

extension MomentVoValueCopy<$R, $Out> on ObjectCopyWith<$R, MomentVo, $Out> {
  MomentVoCopyWith<$R, MomentVo, $Out> get $asMomentVo =>
      $base.as((v, t, t2) => _MomentVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentVoCopyWith<$R, $In extends MomentVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, MomentImageVo,
      MomentImageVoCopyWith<$R, MomentImageVo, MomentImageVo>>? get images;
  UserCopyWith<$R, User, User> get publisher;
  $R call(
      {int? id,
      int? userId,
      String? content,
      List<MomentImageVo>? images,
      String? momentType,
      String? typeSpecificData,
      String? tag,
      num? fishCatch,
      int? fishingSpotId,
      String? fishingSpotName,
      double? longitude,
      double? latitude,
      String? province,
      String? city,
      String? county,
      String? addressDetail,
      int? likeCount,
      int? numberOfDislikes,
      int? status,
      int? follows,
      bool? followed,
      bool? liked,
      bool? disliked,
      String? createdAt,
      String? updateTime,
      User? publisher,
      num? numberOfComments,
      num? followCount,
      num? attentionCount,
      num? momentCount,
      num? commentCount,
      num? viewCount});
  MomentVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _MomentVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentVo, $Out>
    implements MomentVoCopyWith<$R, MomentVo, $Out> {
  _MomentVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentVo> $mapper =
      MomentVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, MomentImageVo,
          MomentImageVoCopyWith<$R, MomentImageVo, MomentImageVo>>?
      get images => $value.images != null
          ? ListCopyWith($value.images!, (v, t) => v.copyWith.$chain(t),
              (v) => call(images: v))
          : null;
  @override
  UserCopyWith<$R, User, User> get publisher =>
      $value.publisher.copyWith.$chain((v) => call(publisher: v));
  @override
  $R call(
          {int? id,
          Object? userId = $none,
          Object? content = $none,
          Object? images = $none,
          Object? momentType = $none,
          Object? typeSpecificData = $none,
          Object? tag = $none,
          Object? fishCatch = $none,
          Object? fishingSpotId = $none,
          Object? fishingSpotName = $none,
          Object? longitude = $none,
          Object? latitude = $none,
          Object? province = $none,
          Object? city = $none,
          Object? county = $none,
          Object? addressDetail = $none,
          Object? likeCount = $none,
          int? numberOfDislikes,
          Object? status = $none,
          Object? follows = $none,
          Object? followed = $none,
          Object? liked = $none,
          Object? disliked = $none,
          Object? createdAt = $none,
          Object? updateTime = $none,
          User? publisher,
          num? numberOfComments,
          num? followCount,
          num? attentionCount,
          num? momentCount,
          num? commentCount,
          num? viewCount}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (userId != $none) #userId: userId,
        if (content != $none) #content: content,
        if (images != $none) #images: images,
        if (momentType != $none) #momentType: momentType,
        if (typeSpecificData != $none) #typeSpecificData: typeSpecificData,
        if (tag != $none) #tag: tag,
        if (fishCatch != $none) #fishCatch: fishCatch,
        if (fishingSpotId != $none) #fishingSpotId: fishingSpotId,
        if (fishingSpotName != $none) #fishingSpotName: fishingSpotName,
        if (longitude != $none) #longitude: longitude,
        if (latitude != $none) #latitude: latitude,
        if (province != $none) #province: province,
        if (city != $none) #city: city,
        if (county != $none) #county: county,
        if (addressDetail != $none) #addressDetail: addressDetail,
        if (likeCount != $none) #likeCount: likeCount,
        if (numberOfDislikes != null) #numberOfDislikes: numberOfDislikes,
        if (status != $none) #status: status,
        if (follows != $none) #follows: follows,
        if (followed != $none) #followed: followed,
        if (liked != $none) #liked: liked,
        if (disliked != $none) #disliked: disliked,
        if (createdAt != $none) #createdAt: createdAt,
        if (updateTime != $none) #updateTime: updateTime,
        if (publisher != null) #publisher: publisher,
        if (numberOfComments != null) #numberOfComments: numberOfComments,
        if (followCount != null) #followCount: followCount,
        if (attentionCount != null) #attentionCount: attentionCount,
        if (momentCount != null) #momentCount: momentCount,
        if (commentCount != null) #commentCount: commentCount,
        if (viewCount != null) #viewCount: viewCount
      }));
  @override
  MomentVo $make(CopyWithData data) => MomentVo(
      id: data.get(#id, or: $value.id),
      userId: data.get(#userId, or: $value.userId),
      content: data.get(#content, or: $value.content),
      images: data.get(#images, or: $value.images),
      momentType: data.get(#momentType, or: $value.momentType),
      typeSpecificData:
          data.get(#typeSpecificData, or: $value.typeSpecificData),
      tag: data.get(#tag, or: $value.tag),
      fishCatch: data.get(#fishCatch, or: $value.fishCatch),
      fishingSpotId: data.get(#fishingSpotId, or: $value.fishingSpotId),
      fishingSpotName: data.get(#fishingSpotName, or: $value.fishingSpotName),
      longitude: data.get(#longitude, or: $value.longitude),
      latitude: data.get(#latitude, or: $value.latitude),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      addressDetail: data.get(#addressDetail, or: $value.addressDetail),
      likeCount: data.get(#likeCount, or: $value.likeCount),
      numberOfDislikes:
          data.get(#numberOfDislikes, or: $value.numberOfDislikes),
      status: data.get(#status, or: $value.status),
      follows: data.get(#follows, or: $value.follows),
      followed: data.get(#followed, or: $value.followed),
      liked: data.get(#liked, or: $value.liked),
      disliked: data.get(#disliked, or: $value.disliked),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updateTime: data.get(#updateTime, or: $value.updateTime),
      publisher: data.get(#publisher, or: $value.publisher),
      numberOfComments:
          data.get(#numberOfComments, or: $value.numberOfComments),
      followCount: data.get(#followCount, or: $value.followCount),
      attentionCount: data.get(#attentionCount, or: $value.attentionCount),
      momentCount: data.get(#momentCount, or: $value.momentCount),
      commentCount: data.get(#commentCount, or: $value.commentCount),
      viewCount: data.get(#viewCount, or: $value.viewCount));

  @override
  MomentVoCopyWith<$R2, MomentVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
