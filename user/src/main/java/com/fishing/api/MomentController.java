package com.fishing.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.moment.MomentComment;
import com.fishing.dto.moment.MomentCommentDTO;
import com.fishing.dto.moment.MomentCreateDTO;
import com.fishing.dto.moment.MomentListDTO;
import com.fishing.dto.moment.MomentUpdateDTO;
import com.fishing.service.MomentCommentService;
import com.fishing.service.MomentLikeService;
import com.fishing.service.MomentService;
import com.fishing.util.JwtTokenUtil;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.moment.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 动态信息接口
 */
@RestController
@RequestMapping("/moments")
@RequiredArgsConstructor
@Tag(name = "动态信息接口")
public class MomentController {

    private final MomentService momentService;
    private final MomentLikeService momentLikeService;
    private final MomentCommentService momentCommentService;
    private final JwtTokenUtil jwtTokenUtil;

    @PostMapping
    @Operation(summary = "发布动态")
    public ResponseEntity<ApiResponse<MomentIdResponse>> createMoment(@RequestBody @Valid MomentCreateDTO dto, Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        Moment moment = new Moment();
        moment.setUserId(userId);
        moment.setContent(dto.getContent());
        moment.setMomentType(dto.getMomentType());
        moment.setVisibility(dto.getVisibility());
        moment.setFishingSpotId(dto.getFishingSpotId());
        moment.setTypeSpecificData(dto.getTypeSpecificData());

        // 创建动态
        Long momentId = momentService.createMoment(moment, dto.getImageUrls());

        return ResponseEntity.ok(ApiResponse.success(new MomentIdResponse(momentId)));
    }

    @PutMapping("/{momentId}")
    @Operation(summary = "更新动态")
    public ResponseEntity<ApiResponse<MomentOperationResponse>> updateMoment(@PathVariable Long momentId, @RequestBody @Valid MomentUpdateDTO dto, Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        Moment moment = new Moment();
        moment.setId(momentId);
        moment.setUserId(userId);
        moment.setContent(dto.getContent());
        moment.setMomentType(dto.getMomentType());
        moment.setVisibility(dto.getVisibility());
        moment.setFishingSpotId(dto.getFishingSpotId());
        moment.setTypeSpecificData(dto.getTypeSpecificData());

        // 更新动态
        boolean success = momentService.updateMoment(moment, dto.getImageUrls());

        return ResponseEntity.ok(ApiResponse.success(new MomentOperationResponse(success)));
    }

    @DeleteMapping("/{momentId}")
    @Operation(summary = "删除动态")
    public ResponseEntity<ApiResponse<MomentOperationResponse>> deleteMoment(
            @PathVariable Long momentId,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        boolean success = momentService.deleteMoment(momentId, userId);

        return ResponseEntity.ok(ApiResponse.success(new MomentOperationResponse(success)));
    }

    @GetMapping("/{momentId}")
    @Operation(summary = "获取动态详情")
    public ResponseEntity<ApiResponse<MomentVO>> getMomentDetail(
            @PathVariable Long momentId,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        MomentVO moment = momentService.getMomentDetail(momentId, userId);
        return ResponseEntity.ok(ApiResponse.success(moment));
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户的动态列表")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getUserMoments(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        Long currentUserId = jwtTokenUtil.getUserId(authentication);
        Page<Moment> pageParam = new Page<>(page, size);
        Page<MomentVO> moments = momentService.getUserMoments(userId, pageParam, currentUserId);
        return ResponseEntity.ok(ApiResponse.success(moments));
    }

    @GetMapping("/following")
    @Operation(summary = "获取关注用户的动态列表")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getFollowingMoments(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        Page<Moment> pageParam = new Page<>(page, size);
        Page<MomentVO> moments = momentService.getFollowingMoments(userId, pageParam);
        return ResponseEntity.ok(ApiResponse.success(moments));
    }

    @GetMapping("/recommended")
    @Operation(summary = "获取推荐动态列表")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getRecommendedMoments(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        Page<Moment> pageParam = new Page<>(page, size);
        Page<MomentVO> moments = momentService.getRecommendedMoments(pageParam, userId);
        return ResponseEntity.ok(ApiResponse.success(moments));
    }

    @GetMapping("/types/{momentType}")
    @Operation(summary = "按类型获取动态列表")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getMomentsByType(
            @PathVariable String momentType,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        Page<Moment> pageParam = new Page<>(page, size);
        Page<MomentVO> moments = momentService.getMomentsByType(momentType, pageParam, userId);
        return ResponseEntity.ok(ApiResponse.success(moments));
    }

    @GetMapping("/spot/{fishingSpotId}")
    @Operation(summary = "获取钓点相关动态列表")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getMomentsByFishingSpot(
            @PathVariable Long fishingSpotId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        Long userId = null;
        if (authentication != null) {
            try {
                userId = jwtTokenUtil.getUserId(authentication);
            } catch (Exception e) {
                userId = null;
            }
        }
        Page<Moment> pageParam = new Page<>(page, size);
        Page<MomentVO> moments = momentService.getMomentsByFishingSpot(fishingSpotId, pageParam, userId);
        return ResponseEntity.ok(ApiResponse.success(moments));
    }

    @PostMapping("/{momentId}/like")
    @Operation(summary = "点赞/取消点赞动态")
    public ResponseEntity<ApiResponse<MomentOperationResponse>> toggleLike(
            @PathVariable Long momentId,
            @RequestParam Boolean isLike,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        boolean success = momentLikeService.toggleLike(momentId, userId, isLike);

        return ResponseEntity.ok(ApiResponse.success(new MomentOperationResponse(success)));
    }

    @GetMapping("/{momentId}/likes")
    @Operation(summary = "获取动态点赞用户列表")
    public ResponseEntity<ApiResponse<Page<User>>> getLikeUsers(
            @PathVariable Long momentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        Page<User> pageParam = new Page<>(page, size);
        Page<User> users = momentLikeService.getLikeUsers(momentId, pageParam);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @PostMapping("/{momentId}/comments")
    @Operation(summary = "评论动态")
    public ResponseEntity<ApiResponse<MomentCommentResponse>> addComment(
            @PathVariable Long momentId,
            @RequestBody @Valid MomentCommentDTO dto,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        MomentComment comment = new MomentComment();
        comment.setMomentId(momentId);
        comment.setUserId(userId);
        comment.setContent(dto.getContent());
        comment.setParentId(dto.getParentId());

        Long commentId = momentCommentService.addComment(comment);

        return ResponseEntity.ok(ApiResponse.success(new MomentCommentResponse(commentId)));
    }

    @DeleteMapping("/comments/{commentId}")
    @Operation(summary = "删除评论")
    public ResponseEntity<ApiResponse<MomentOperationResponse>> deleteComment(
            @PathVariable Long commentId,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        boolean success = momentCommentService.deleteComment(commentId, userId);

        return ResponseEntity.ok(ApiResponse.success(new MomentOperationResponse(success)));
    }

    @GetMapping("/{momentId}/comments")
    @Operation(summary = "获取动态评论列表")
    public ResponseEntity<ApiResponse<Page<MomentCommentVO>>> getComments(
            @PathVariable Long momentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        Page<MomentComment> pageParam = new Page<>(page, size);
        Page<MomentCommentVO> comments = momentCommentService.getCommentsByMomentId(momentId, pageParam);
        return ResponseEntity.ok(ApiResponse.success(comments));
    }

    @GetMapping("/comments/{commentId}/replies")
    @Operation(summary = "获取评论回复列表")
    public ResponseEntity<ApiResponse<Page<MomentCommentVO>>> getReplies(
            @PathVariable Long commentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        Page<MomentComment> pageParam = new Page<>(page, size);
        Page<MomentCommentVO> replies = momentCommentService.getRepliesByCommentId(commentId, pageParam);
        return ResponseEntity.ok(ApiResponse.success(replies));
    }

    @PostMapping("/list")
    @Operation(summary = "获取动态列表（支持筛选条件）")
    public ResponseEntity<ApiResponse<Page<MomentVO>>> getMomentsList(
            @RequestBody @Valid MomentListDTO dto,
            Authentication authentication) {
        // 获取当前用户ID（可能为空，支持未登录用户访问）
        Long currentUserId = null;
        if (authentication != null) {
            try {
                currentUserId = jwtTokenUtil.getUserId(authentication);
            } catch (Exception e) {
                // 忽略认证异常，允许未登录用户访问
                currentUserId = null;
            }
        }
        
        // 构建分页参数
        Page<Moment> pageParam = new Page<>(
            dto.getPageNum() != null ? dto.getPageNum() : 1,
            dto.getPageSize() != null ? dto.getPageSize() : 10
        );
        
        // 调用服务方法获取动态列表
        Page<MomentVO> moments = momentService.getMomentsWithFilters(
            dto.getUserId(),
            dto.getTag(),
            dto.getProvince(),
            dto.getCity(),
            dto.getCounty(),
            pageParam,
            currentUserId
        );
        
        return ResponseEntity.ok(ApiResponse.success(moments));
    }
}