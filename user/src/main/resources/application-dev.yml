spring:
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 60000
      connection-timeout: 30000
      max-lifetime: 1800000
      pool-name: fishingHikariCP
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************************
    username: root
    password: s4l$q*mzxRt
  data:
    redis:
      host: ************
      port: 6379
      database: 0
      password: JFwasd12!wW
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0
