package com.fishing.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 举报用户请求DTO
 */
@Data
@Schema(description = "举报用户请求参数")
public class ReportUserDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 举报原因
     */
    @NotBlank(message = "举报原因不能为空")
    @Schema(description = "举报原因")
    private String reason;

    /**
     * 详细描述
     */
    @Schema(description = "详细描述")
    private String description;
}
